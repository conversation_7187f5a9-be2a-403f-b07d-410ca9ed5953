#!/usr/bin/env python3
"""
LangGraph PDF Analysis Agent
Iteratively processes PDFs, taking notes and learning across multiple passes.
"""

import os
import json
import time
import requests
from pathlib import Path
from typing import Dict, List, Any, TypedDict
from dataclasses import dataclass, asdict
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_analysis.log'),
        logging.StreamHandler()
    ]
)

# OpenRouter Configuration
OPENROUTER_API_KEY = "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"
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
MODEL = "anthropic/claude-3.7-sonnet:thinking"

# Configuration
CHUNK_SIZE = 300  # Lines per chunk
RATE_LIMIT_DELAY = 2  # Seconds between API calls
MAX_PASSES = 3  # Number of analysis passes

@dataclass
class DocumentChunk:
    """Represents a chunk of document content."""
    file_path: str
    chunk_id: int
    start_line: int
    end_line: int
    content: str
    total_lines: int

@dataclass
class Note:
    """Represents a note taken during analysis."""
    document: str
    chunk_id: int
    pass_number: int
    timestamp: str
    category: str
    content: str
    importance: int  # 1-5 scale
    related_docs: List[str]

class AgentState(TypedDict):
    """State for the LangGraph agent."""
    current_pass: int
    documents: List[str]
    current_doc_index: int
    current_chunk_index: int
    notes: List[Note]
    learned_patterns: Dict[str, Any]
    analysis_complete: bool
    error_count: int

class PDFAnalysisAgent:
    """LangGraph agent for iterative PDF analysis."""

    def __init__(self):
        self.state = AgentState(
            current_pass=1,
            documents=[],
            current_doc_index=0,
            current_chunk_index=0,
            notes=[],
            learned_patterns={},
            analysis_complete=False,
            error_count=0
        )
        self.extracted_dir = Path("extracted_pdfs")
        self.output_dir = Path("analysis_output")
        self.output_dir.mkdir(exist_ok=True)

    def call_openrouter(self, messages: List[Dict], max_retries: int = 3) -> str:
        """Call OpenRouter API with retry logic."""
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/roofing-analysis",
            "X-Title": "PDF Analysis Agent"
        }

        data = {
            "model": MODEL,
            "messages": messages,
            "temperature": 0.3,
            "max_tokens": 2000
        }

        for attempt in range(max_retries):
            try:
                response = requests.post(
                    OPENROUTER_URL,
                    headers=headers,
                    data=json.dumps(data),
                    timeout=60
                )
                response.raise_for_status()

                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
                else:
                    logging.error(f"Unexpected API response: {result}")

            except requests.exceptions.RequestException as e:
                logging.error(f"API call attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(RATE_LIMIT_DELAY * (attempt + 1))

        raise Exception(f"Failed to call OpenRouter API after {max_retries} attempts")

    def load_documents(self) -> List[str]:
        """Load all extracted PDF text files."""
        documents = []
        for file_path in self.extracted_dir.glob("extracted_*.txt"):
            if file_path.is_file():
                documents.append(str(file_path))

        logging.info(f"Found {len(documents)} extracted PDF files")
        return sorted(documents)

    def chunk_document(self, file_path: str) -> List[DocumentChunk]:
        """Split document into manageable chunks."""
        chunks = []

        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        total_lines = len(lines)
        chunk_id = 0

        for start_line in range(0, total_lines, CHUNK_SIZE):
            end_line = min(start_line + CHUNK_SIZE, total_lines)
            content = ''.join(lines[start_line:end_line])

            chunk = DocumentChunk(
                file_path=file_path,
                chunk_id=chunk_id,
                start_line=start_line,
                end_line=end_line,
                content=content,
                total_lines=total_lines
            )
            chunks.append(chunk)
            chunk_id += 1

        return chunks

    def analyze_chunk(self, chunk: DocumentChunk, pass_number: int) -> List[Note]:
        """Analyze a document chunk and extract notes."""

        # Build context from previous notes and learned patterns
        context_notes = [note for note in self.state['notes']
                        if note.importance >= 3]  # Only high-importance notes

        learned_context = ""
        if self.state['learned_patterns']:
            learned_context = f"\nLearned patterns from previous analysis:\n{json.dumps(self.state['learned_patterns'], indent=2)}"

        previous_notes_context = ""
        if context_notes:
            previous_notes_context = f"\nPrevious important notes:\n" + "\n".join([
                f"- {note.category}: {note.content}" for note in context_notes[-10:]  # Last 10 notes
            ])

        # Create analysis prompt based on pass number
        if pass_number == 1:
            analysis_focus = """Focus on:
1. Key business metrics, statistics, and data points
2. Market insights and industry trends
3. Strategic frameworks and methodologies
4. Important processes and best practices
5. Critical pain points and challenges mentioned"""
        elif pass_number == 2:
            analysis_focus = """Focus on:
1. Connections between documents and cross-references
2. Contradictions or conflicting information
3. Gaps in the analysis from pass 1
4. Deeper strategic insights
5. Implementation details and tactical advice"""
        else:
            analysis_focus = """Focus on:
1. Synthesis and integration of all information
2. Meta-insights and overarching themes
3. Final validation of key findings
4. Strategic recommendations
5. Action items and next steps"""

        messages = [
            {
                "role": "system",
                "content": f"""You are an expert business analyst reviewing roofing industry documents.

This is PASS {pass_number} of {MAX_PASSES}.

{analysis_focus}

Extract 3-7 high-quality notes from this content chunk. Each note should be:
- Specific and actionable
- Categorized (e.g., "Market Data", "Strategy", "Pain Point", "Opportunity", "Process")
- Rated for importance (1-5, where 5 is critical)
- Connected to other documents when relevant

Return your response as a JSON array of notes with this structure:
[
  {{
    "category": "category_name",
    "content": "detailed note content",
    "importance": 1-5,
    "related_docs": ["doc1", "doc2"] // if applicable
  }}
]

{learned_context}
{previous_notes_context}"""
            },
            {
                "role": "user",
                "content": f"""Document: {Path(chunk.file_path).name}
Chunk {chunk.chunk_id + 1} of {chunk.total_lines // CHUNK_SIZE + 1}
Lines {chunk.start_line + 1}-{chunk.end_line}

Content:
{chunk.content}

Please analyze this chunk and extract key insights as JSON notes."""
            }
        ]

        try:
            response = self.call_openrouter(messages)
            time.sleep(RATE_LIMIT_DELAY)  # Rate limiting

            # Clean response - remove markdown code blocks if present
            cleaned_response = response.strip()
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response[7:]  # Remove ```json
            if cleaned_response.endswith('```'):
                cleaned_response = cleaned_response[:-3]  # Remove ```
            cleaned_response = cleaned_response.strip()

            # Parse JSON response
            notes_data = json.loads(cleaned_response)

            notes = []
            for note_data in notes_data:
                note = Note(
                    document=Path(chunk.file_path).name,
                    chunk_id=chunk.chunk_id,
                    pass_number=pass_number,
                    timestamp=datetime.now().isoformat(),
                    category=note_data.get('category', 'General'),
                    content=note_data.get('content', ''),
                    importance=note_data.get('importance', 3),
                    related_docs=note_data.get('related_docs', [])
                )
                notes.append(note)

            logging.info(f"Extracted {len(notes)} notes from {Path(chunk.file_path).name} chunk {chunk.chunk_id}")
            return notes

        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response: {e}")
            logging.error(f"Response was: {response}")
            return []
        except Exception as e:
            logging.error(f"Error analyzing chunk: {e}")
            self.state['error_count'] += 1
            return []

    def update_learned_patterns(self):
        """Update learned patterns based on accumulated notes."""
        if not self.state['notes']:
            return

        # Analyze patterns in notes
        categories = {}
        importance_trends = {}
        document_connections = {}

        for note in self.state['notes']:
            # Category frequency
            categories[note.category] = categories.get(note.category, 0) + 1

            # Importance by category
            if note.category not in importance_trends:
                importance_trends[note.category] = []
            importance_trends[note.category].append(note.importance)

            # Document connections
            doc_key = note.document
            if doc_key not in document_connections:
                document_connections[doc_key] = set()
            document_connections[doc_key].update(note.related_docs)

        # Calculate average importance by category
        avg_importance = {}
        for category, scores in importance_trends.items():
            avg_importance[category] = sum(scores) / len(scores)

        self.state['learned_patterns'] = {
            'category_frequency': categories,
            'category_importance': avg_importance,
            'document_connections': {k: list(v) for k, v in document_connections.items()},
            'total_notes': len(self.state['notes']),
            'high_importance_notes': len([n for n in self.state['notes'] if n.importance >= 4])
        }

        logging.info(f"Updated learned patterns: {len(categories)} categories, "
                    f"{self.state['learned_patterns']['high_importance_notes']} high-importance notes")

    def process_document(self, file_path: str, pass_number: int):
        """Process a single document in chunks."""
        logging.info(f"Processing {Path(file_path).name} - Pass {pass_number}")

        chunks = self.chunk_document(file_path)

        for chunk in chunks:
            notes = self.analyze_chunk(chunk, pass_number)
            self.state['notes'].extend(notes)

            # Update learned patterns periodically
            if len(self.state['notes']) % 20 == 0:
                self.update_learned_patterns()

        logging.info(f"Completed {Path(file_path).name} - {len(chunks)} chunks processed")

    def run_analysis_pass(self, pass_number: int):
        """Run a complete analysis pass through all documents."""
        logging.info(f"Starting analysis pass {pass_number}/{MAX_PASSES}")

        documents = self.load_documents()
        self.state['documents'] = documents

        for doc_path in documents:
            self.process_document(doc_path, pass_number)

        # Update patterns after each pass
        self.update_learned_patterns()

        logging.info(f"Completed pass {pass_number} - {len(self.state['notes'])} total notes")

    def generate_synthesis_report(self):
        """Generate a comprehensive synthesis report."""
        messages = [
            {
                "role": "system",
                "content": """You are an expert business strategist creating a comprehensive synthesis report
from analyzed roofing industry documents.

Create a detailed executive summary that includes:
1. Key Market Insights & Data
2. Strategic Opportunities
3. Critical Pain Points & Challenges
4. Recommended Actions
5. Implementation Priorities

Use the notes provided to create actionable insights and strategic recommendations.
Format as a professional business report with clear sections and bullet points."""
            },
            {
                "role": "user",
                "content": f"""Based on the following {len(self.state['notes'])} notes from {MAX_PASSES} analysis passes,
create a comprehensive synthesis report:

LEARNED PATTERNS:
{json.dumps(self.state['learned_patterns'], indent=2)}

HIGH-IMPORTANCE NOTES:
{chr(10).join([f"• [{note.category}] {note.content}" for note in self.state['notes'] if note.importance >= 4])}

ALL NOTES BY CATEGORY:
{self._format_notes_by_category()}

Please create a strategic synthesis report."""
            }
        ]

        try:
            synthesis = self.call_openrouter(messages)
            return synthesis
        except Exception as e:
            logging.error(f"Error generating synthesis: {e}")
            return "Error generating synthesis report"

    def _format_notes_by_category(self) -> str:
        """Format notes grouped by category."""
        categories = {}
        for note in self.state['notes']:
            if note.category not in categories:
                categories[note.category] = []
            categories[note.category].append(note)

        formatted = ""
        for category, notes in sorted(categories.items()):
            formatted += f"\n{category.upper()}:\n"
            for note in sorted(notes, key=lambda x: x.importance, reverse=True)[:10]:  # Top 10 per category
                formatted += f"  • [Importance {note.importance}] {note.content}\n"

        return formatted

    def save_results(self):
        """Save analysis results to files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save all notes as JSON
        notes_file = self.output_dir / f"notes_{timestamp}.json"
        with open(notes_file, 'w', encoding='utf-8') as f:
            notes_data = [asdict(note) for note in self.state['notes']]
            json.dump(notes_data, f, indent=2, ensure_ascii=False)

        # Save learned patterns
        patterns_file = self.output_dir / f"patterns_{timestamp}.json"
        with open(patterns_file, 'w', encoding='utf-8') as f:
            json.dump(self.state['learned_patterns'], f, indent=2)

        # Generate and save synthesis report
        synthesis = self.generate_synthesis_report()
        synthesis_file = self.output_dir / f"synthesis_report_{timestamp}.md"
        with open(synthesis_file, 'w', encoding='utf-8') as f:
            f.write(f"# PDF Analysis Synthesis Report\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Total Notes:** {len(self.state['notes'])}\n")
            f.write(f"**Analysis Passes:** {MAX_PASSES}\n")
            f.write(f"**Documents Analyzed:** {len(self.state['documents'])}\n\n")
            f.write(synthesis)

        # Save summary statistics
        stats_file = self.output_dir / f"statistics_{timestamp}.md"
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write("# Analysis Statistics\n\n")
            f.write(f"- **Total Notes:** {len(self.state['notes'])}\n")
            f.write(f"- **High Importance Notes (4-5):** {len([n for n in self.state['notes'] if n.importance >= 4])}\n")
            f.write(f"- **Documents Processed:** {len(self.state['documents'])}\n")
            f.write(f"- **Analysis Passes:** {MAX_PASSES}\n")
            f.write(f"- **Error Count:** {self.state['error_count']}\n\n")

            f.write("## Notes by Category\n")
            categories = {}
            for note in self.state['notes']:
                categories[note.category] = categories.get(note.category, 0) + 1

            for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
                f.write(f"- **{category}:** {count} notes\n")

        logging.info(f"Results saved to {self.output_dir}")
        logging.info(f"- Notes: {notes_file}")
        logging.info(f"- Patterns: {patterns_file}")
        logging.info(f"- Synthesis: {synthesis_file}")
        logging.info(f"- Statistics: {stats_file}")

    def run(self):
        """Main execution method."""
        logging.info("Starting PDF Analysis Agent")

        try:
            # Run multiple analysis passes
            for pass_num in range(1, MAX_PASSES + 1):
                self.state['current_pass'] = pass_num
                self.run_analysis_pass(pass_num)

                # Brief pause between passes
                if pass_num < MAX_PASSES:
                    logging.info(f"Pausing before pass {pass_num + 1}...")
                    time.sleep(5)

            # Final analysis and save results
            self.state['analysis_complete'] = True
            self.save_results()

            logging.info("PDF Analysis Agent completed successfully!")
            logging.info(f"Total notes generated: {len(self.state['notes'])}")
            logging.info(f"High-importance notes: {len([n for n in self.state['notes'] if n.importance >= 4])}")

        except Exception as e:
            logging.error(f"Analysis failed: {e}")
            raise

def main():
    """Main entry point."""
    agent = PDFAnalysisAgent()
    agent.run()

if __name__ == "__main__":
    main()
