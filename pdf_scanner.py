#!/usr/bin/env python3
"""
PDF Scanner Tool
Scans all PDF files in the workspace and extracts their text content.
"""

import os
import sys
from pathlib import Path
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_scan.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def install_dependencies():
    """Install required dependencies for PDF processing."""
    import subprocess
    
    dependencies = [
        'PyPDF2',
        'pdfplumber', 
        'python-docx'
    ]
    
    for dep in dependencies:
        try:
            __import__(dep.replace('-', '_').lower())
            logging.info(f"✓ {dep} already installed")
        except ImportError:
            logging.info(f"Installing {dep}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
            logging.info(f"✓ {dep} installed successfully")

def find_pdf_files(directory="."):
    """Find all PDF files in the given directory."""
    pdf_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    return pdf_files

def extract_text_pypdf2(pdf_path):
    """Extract text using PyPDF2."""
    try:
        import PyPDF2
        text = ""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text += f"\n--- Page {page_num + 1} ---\n"
                        text += page_text + "\n"
                except Exception as e:
                    logging.warning(f"Error extracting page {page_num + 1} from {pdf_path}: {e}")
        return text
    except Exception as e:
        logging.error(f"PyPDF2 extraction failed for {pdf_path}: {e}")
        return None

def extract_text_pdfplumber(pdf_path):
    """Extract text using pdfplumber (more reliable for complex layouts)."""
    try:
        import pdfplumber
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text += f"\n--- Page {page_num + 1} ---\n"
                        text += page_text + "\n"
                except Exception as e:
                    logging.warning(f"Error extracting page {page_num + 1} from {pdf_path}: {e}")
        return text
    except Exception as e:
        logging.error(f"pdfplumber extraction failed for {pdf_path}: {e}")
        return None

def extract_pdf_text(pdf_path):
    """Extract text from PDF using multiple methods for best results."""
    logging.info(f"Processing: {pdf_path}")
    
    # Try pdfplumber first (usually better for complex layouts)
    text = extract_text_pdfplumber(pdf_path)
    
    # Fallback to PyPDF2 if pdfplumber fails
    if not text or len(text.strip()) < 100:
        logging.info(f"Trying PyPDF2 for {pdf_path}")
        text = extract_text_pypdf2(pdf_path)
    
    if not text or len(text.strip()) < 50:
        logging.warning(f"Could not extract meaningful text from {pdf_path}")
        return f"[ERROR: Could not extract text from {pdf_path}]"
    
    return text

def create_output_filename(pdf_path):
    """Create a clean output filename for the extracted text."""
    base_name = Path(pdf_path).stem
    # Clean up the filename
    clean_name = base_name.replace(' ', '_').replace('(', '').replace(')', '').replace('–', '-')
    return f"extracted_{clean_name}.txt"

def scan_all_pdfs():
    """Main function to scan all PDFs and extract their content."""
    logging.info("Starting PDF scanning process...")
    
    # Install dependencies
    install_dependencies()
    
    # Find all PDF files
    pdf_files = find_pdf_files()
    
    if not pdf_files:
        logging.info("No PDF files found in the workspace.")
        return
    
    logging.info(f"Found {len(pdf_files)} PDF files:")
    for pdf in pdf_files:
        logging.info(f"  - {pdf}")
    
    # Create output directory
    output_dir = "extracted_pdfs"
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each PDF
    results = []
    for pdf_path in pdf_files:
        try:
            # Extract text
            text_content = extract_pdf_text(pdf_path)
            
            # Create output file
            output_filename = create_output_filename(pdf_path)
            output_path = os.path.join(output_dir, output_filename)
            
            # Write extracted text to file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(f"# Extracted from: {pdf_path}\n")
                f.write(f"# Extraction date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# File size: {os.path.getsize(pdf_path)} bytes\n\n")
                f.write(text_content)
            
            results.append({
                'pdf_path': pdf_path,
                'output_path': output_path,
                'success': True,
                'text_length': len(text_content),
                'error': None
            })
            
            logging.info(f"✓ Successfully extracted {len(text_content)} characters from {pdf_path}")
            
        except Exception as e:
            error_msg = f"Failed to process {pdf_path}: {str(e)}"
            logging.error(error_msg)
            results.append({
                'pdf_path': pdf_path,
                'output_path': None,
                'success': False,
                'text_length': 0,
                'error': error_msg
            })
    
    # Generate summary report
    generate_summary_report(results, output_dir)
    
    logging.info("PDF scanning completed!")

def generate_summary_report(results, output_dir):
    """Generate a summary report of the scanning process."""
    report_path = os.path.join(output_dir, "scan_summary.md")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# PDF Scanning Summary Report\n\n")
        f.write(f"**Scan Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"**Total PDFs Found:** {len(results)}\n")
        f.write(f"**Successfully Processed:** {sum(1 for r in results if r['success'])}\n")
        f.write(f"**Failed:** {sum(1 for r in results if not r['success'])}\n\n")
        
        f.write("## Processing Results\n\n")
        
        for result in results:
            status = "✅ SUCCESS" if result['success'] else "❌ FAILED"
            f.write(f"### {Path(result['pdf_path']).name}\n")
            f.write(f"- **Status:** {status}\n")
            f.write(f"- **Source:** `{result['pdf_path']}`\n")
            
            if result['success']:
                f.write(f"- **Output:** `{result['output_path']}`\n")
                f.write(f"- **Text Length:** {result['text_length']:,} characters\n")
            else:
                f.write(f"- **Error:** {result['error']}\n")
            f.write("\n")
        
        f.write("## Next Steps\n\n")
        f.write("1. Review the extracted text files in the `extracted_pdfs/` directory\n")
        f.write("2. Check for any files that failed extraction and may need manual review\n")
        f.write("3. Use the extracted content for analysis, search, or further processing\n")

if __name__ == "__main__":
    scan_all_pdfs()
