#!/usr/bin/env python3
"""
LangGraph Recursive Multi-Agent RAG System for Roofing Offer + Copy Generation
Uses the analyzed PDF data to generate high-converting offers and landing pages.
"""

import os
import json
import time
import requests
from pathlib import Path
from typing import Dict, List, Any, TypedDict
from dataclasses import dataclass
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('offer_generator.log'),
        logging.StreamHandler()
    ]
)

# OpenRouter Configuration
OPENROUTER_API_KEY = "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"
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
MODEL = "anthropic/claude-3.7-sonnet:thinking"

# State for the LangGraph workflow
class WorkflowState(TypedDict):
    docs: str
    insights: str
    offer: str
    copy: str
    final_output: str
    route: str
    iteration_count: int
    score: float

class RoofingOfferGenerator:
    """Multi-agent system for generating roofing offers and copy."""

    def __init__(self):
        self.analysis_dir = Path("analysis_output")
        self.output_dir = Path("generated_offers")
        self.output_dir.mkdir(exist_ok=True)
        self.max_iterations = 3

    def call_openrouter(self, messages: List[Dict], max_retries: int = 3) -> str:
        """Call OpenRouter API with retry logic."""
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/roofing-offer-generator",
            "X-Title": "Roofing Offer Generator"
        }

        data = {
            "model": MODEL,
            "messages": messages,
            "temperature": 0.3,
            "max_tokens": 3000
        }

        for attempt in range(max_retries):
            try:
                response = requests.post(
                    OPENROUTER_URL,
                    headers=headers,
                    data=json.dumps(data),
                    timeout=60
                )
                response.raise_for_status()

                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
                else:
                    logging.error(f"Unexpected API response: {result}")

            except requests.exceptions.RequestException as e:
                logging.error(f"API call attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 * (attempt + 1))

        raise Exception(f"Failed to call OpenRouter API after {max_retries} attempts")

    def load_analysis_data(self) -> str:
        """Load the analyzed PDF data as context."""
        try:
            # Load the synthesis report
            synthesis_files = list(self.analysis_dir.glob("synthesis_report_*.md"))
            if not synthesis_files:
                raise FileNotFoundError("No synthesis report found")

            with open(synthesis_files[0], 'r', encoding='utf-8') as f:
                synthesis = f.read()

            # Load high-importance notes
            notes_files = list(self.analysis_dir.glob("notes_*.json"))
            if notes_files:
                with open(notes_files[0], 'r', encoding='utf-8') as f:
                    notes_data = json.load(f)

                # Filter high-importance notes
                high_importance_notes = [
                    note for note in notes_data
                    if note.get('importance', 0) >= 4
                ]

                notes_summary = "\n".join([
                    f"• [{note['category']}] {note['content']}"
                    for note in high_importance_notes[:50]  # Top 50 notes
                ])
            else:
                notes_summary = ""

            combined_context = f"""
# ROOFING INDUSTRY ANALYSIS CONTEXT

## EXECUTIVE SYNTHESIS
{synthesis}

## HIGH-IMPORTANCE INSIGHTS
{notes_summary}
"""
            return combined_context

        except Exception as e:
            logging.error(f"Error loading analysis data: {e}")
            return "No analysis data available"

    def market_analysis_node(self, state: WorkflowState) -> WorkflowState:
        """Extract market insights for offer creation."""
        logging.info("Running market analysis...")

        messages = [
            {
                "role": "system",
                "content": """You are a market analyst focused on the roofing industry. Extract specific insights about:
- Lead generation pain points and bottlenecks
- Buying behavior and decision factors
- Common objections and concerns
- Current solutions roofers rely on and their limitations
- Pricing sensitivity and value perception
- Technology adoption gaps and opportunities

Create a bullet list of 8-12 actionable insights that can inform offer creation."""
            },
            {
                "role": "user",
                "content": f"""Based on this roofing industry analysis, extract key market insights:

{state['docs']}

Focus on insights that would help create a compelling offer for roofing contractors."""
            }
        ]

        try:
            insights = self.call_openrouter(messages)
            time.sleep(2)  # Rate limiting

            state['insights'] = insights
            logging.info("Market analysis completed")
            return state

        except Exception as e:
            logging.error(f"Error in market analysis: {e}")
            state['insights'] = "Market analysis failed"
            return state

    def offer_builder_node(self, state: WorkflowState) -> WorkflowState:
        """Build a compelling offer based on market insights."""
        logging.info("Building offer...")

        messages = [
            {
                "role": "system",
                "content": """You're building a high-converting offer for roofing contractors. Create an offer that:

1. Solves their biggest pain points directly
2. Feels tailored specifically to roofers
3. Includes specific, tangible deliverables
4. Has a strong guarantee or risk reversal
5. Uses performance-based or results-oriented positioning
6. Addresses their skepticism about marketing vendors

Structure your offer with:
- Core promise/outcome
- Specific deliverables/components
- Bonuses that increase perceived value
- Guarantee that removes risk
- Clear call-to-action

Do not explain your thinking — just write the offer in a compelling, direct format."""
            },
            {
                "role": "user",
                "content": f"""Based on these market insights, create a compelling offer:

{state['insights']}

Make it irresistible to roofing contractors who are frustrated with current lead generation and want predictable growth."""
            }
        ]

        try:
            offer = self.call_openrouter(messages)
            time.sleep(2)  # Rate limiting

            state['offer'] = offer
            logging.info("Offer building completed")
            return state

        except Exception as e:
            logging.error(f"Error in offer building: {e}")
            state['offer'] = "Offer building failed"
            return state

    def copywriter_node(self, state: WorkflowState) -> WorkflowState:
        """Write a longform landing page."""
        logging.info("Writing landing page copy...")

        messages = [
            {
                "role": "system",
                "content": """Write a longform landing page that:

1. Hooks the reader immediately with a compelling headline
2. Flows logically: pain → agitation → solution → offer → proof → CTA
3. Uses dark theme structure (black background, red/bright accents implied)
4. Sounds confident, clean, and built for mobile scanning
5. Includes specific roofing industry language and pain points
6. Has multiple CTAs throughout the page
7. Uses social proof and testimonials strategically
8. Addresses common objections

Structure:
- Compelling headline and subheadline
- Problem agitation section
- Solution introduction
- Offer presentation with clear value stack
- Social proof and testimonials
- Objection handling
- Final CTA with urgency/scarcity

Write in a direct, no-nonsense tone that resonates with contractors."""
            },
            {
                "role": "user",
                "content": f"""Write a complete longform landing page for this offer:

{state['offer']}

Target audience: Residential roofing contractors who are frustrated with inconsistent leads and want predictable growth.

Make it conversion-optimized and mobile-friendly."""
            }
        ]

        try:
            copy = self.call_openrouter(messages)
            time.sleep(2)  # Rate limiting

            state['copy'] = copy
            logging.info("Copywriting completed")
            return state

        except Exception as e:
            logging.error(f"Error in copywriting: {e}")
            state['copy'] = "Copywriting failed"
            return state

    def refiner_node(self, state: WorkflowState) -> WorkflowState:
        """Refine and score the landing page copy."""
        logging.info("Refining copy...")

        messages = [
            {
                "role": "system",
                "content": """Review and refine the landing page copy by:

1. Sharpening clarity and removing fluff
2. Making claims more specific with numbers/metrics
3. Improving emotional pull without hype
4. Ensuring mobile-friendly scanning
5. Strengthening the value proposition
6. Improving flow and transitions

Return a JSON object with exactly this structure:
{
  "score": 0.85,
  "copy": "refined copy here",
  "improvements": "list of changes made"
}

Score from 0.0 to 1.0 based on:
- Clarity and specificity (0.3)
- Emotional resonance (0.2)
- Mobile optimization (0.2)
- Conversion elements (0.3)

If score < 0.85, it needs more work."""
            },
            {
                "role": "user",
                "content": f"""Refine this landing page copy:

{state['copy']}

Make it more compelling, specific, and conversion-focused for roofing contractors."""
            }
        ]

        try:
            result = self.call_openrouter(messages)
            time.sleep(2)  # Rate limiting

            # Clean and parse JSON response
            cleaned_result = result.strip()
            if cleaned_result.startswith('```json'):
                cleaned_result = cleaned_result[7:]
            if cleaned_result.endswith('```'):
                cleaned_result = cleaned_result[:-3]
            cleaned_result = cleaned_result.strip()

            try:
                output = json.loads(cleaned_result)
                score = float(output.get("score", 0))
                refined_copy = output.get("copy", state['copy'])

                state['copy'] = refined_copy
                state['score'] = score
                state['iteration_count'] = state.get('iteration_count', 0) + 1

                # Determine routing
                if score < 0.85 and state['iteration_count'] < self.max_iterations:
                    state['route'] = "critic"
                    logging.info(f"Score {score:.2f} < 0.85, routing to critic (iteration {state['iteration_count']})")
                else:
                    state['route'] = "done"
                    state['final_output'] = refined_copy
                    logging.info(f"Refinement completed with score {score:.2f}")

                return state

            except json.JSONDecodeError as e:
                logging.error(f"Failed to parse refiner JSON: {e}")
                # Fallback: treat as final output
                state['final_output'] = result
                state['route'] = "done"
                state['score'] = 0.8
                return state

        except Exception as e:
            logging.error(f"Error in refinement: {e}")
            state['final_output'] = state['copy']
            state['route'] = "done"
            state['score'] = 0.7
            return state

    def critic_node(self, state: WorkflowState) -> WorkflowState:
        """Provide critical feedback and improvements."""
        logging.info("Running critic analysis...")

        messages = [
            {
                "role": "system",
                "content": """You are a critical reviewer of landing page copy for roofing contractors.

Identify specific flaws, gaps, or vague sections. Focus on:
1. Weak or generic value propositions
2. Missing specificity (numbers, timelines, guarantees)
3. Poor flow or confusing sections
4. Weak emotional hooks
5. Missing objection handling
6. Unclear or weak CTAs

Rewrite any parts that could be more specific, persuasive, or aligned with roofer pain points.

Return ONLY the improved version of the copy, not explanations."""
            },
            {
                "role": "user",
                "content": f"""Review and improve this landing page copy for roofing contractors:

{state['copy']}

Current score: {state.get('score', 0):.2f}
Iteration: {state.get('iteration_count', 0)}

Make it more compelling and conversion-focused."""
            }
        ]

        try:
            improved_copy = self.call_openrouter(messages)
            time.sleep(2)  # Rate limiting

            state['copy'] = improved_copy
            logging.info("Critic analysis completed")
            return state

        except Exception as e:
            logging.error(f"Error in critic analysis: {e}")
            return state

    def run_workflow(self) -> Dict[str, Any]:
        """Execute the complete workflow."""
        logging.info("Starting roofing offer generation workflow...")

        # Initialize state
        docs = self.load_analysis_data()
        state = WorkflowState(
            docs=docs,
            insights="",
            offer="",
            copy="",
            final_output="",
            route="",
            iteration_count=0,
            score=0.0
        )

        try:
            # Step 1: Market Analysis
            state = self.market_analysis_node(state)

            # Step 2: Offer Building
            state = self.offer_builder_node(state)

            # Step 3: Copywriting
            state = self.copywriter_node(state)

            # Step 4: Refinement Loop
            while True:
                state = self.refiner_node(state)

                if state['route'] == "done":
                    break
                elif state['route'] == "critic":
                    state = self.critic_node(state)
                else:
                    break  # Safety exit

            # Save results
            self.save_results(state)

            logging.info("Workflow completed successfully!")
            return {
                "success": True,
                "final_score": state.get('score', 0),
                "iterations": state.get('iteration_count', 0),
                "output_length": len(state.get('final_output', '')),
                "state": state
            }

        except Exception as e:
            logging.error(f"Workflow failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "state": state
            }

    def save_results(self, state: WorkflowState):
        """Save the generated offer and copy."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save complete results as JSON
        results_file = self.output_dir / f"offer_generation_results_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                "timestamp": timestamp,
                "insights": state.get('insights', ''),
                "offer": state.get('offer', ''),
                "final_copy": state.get('final_output', ''),
                "score": state.get('score', 0),
                "iterations": state.get('iteration_count', 0)
            }, f, indent=2, ensure_ascii=False)

        # Save landing page copy as markdown
        copy_file = self.output_dir / f"landing_page_{timestamp}.md"
        with open(copy_file, 'w', encoding='utf-8') as f:
            f.write(f"# Generated Landing Page\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Final Score:** {state.get('score', 0):.2f}\n")
            f.write(f"**Iterations:** {state.get('iteration_count', 0)}\n\n")
            f.write("## Market Insights\n")
            f.write(state.get('insights', '') + "\n\n")
            f.write("## Offer\n")
            f.write(state.get('offer', '') + "\n\n")
            f.write("## Landing Page Copy\n")
            f.write(state.get('final_output', ''))

        # Save offer as separate file
        offer_file = self.output_dir / f"offer_{timestamp}.md"
        with open(offer_file, 'w', encoding='utf-8') as f:
            f.write(f"# Generated Offer\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(state.get('offer', ''))

        logging.info(f"Results saved:")
        logging.info(f"- Complete results: {results_file}")
        logging.info(f"- Landing page: {copy_file}")
        logging.info(f"- Offer: {offer_file}")

def main():
    """Main entry point."""
    generator = RoofingOfferGenerator()
    results = generator.run_workflow()

    if results['success']:
        print(f"\n🎉 Offer generation completed successfully!")
        print(f"📊 Final Score: {results['final_score']:.2f}")
        print(f"🔄 Iterations: {results['iterations']}")
        print(f"📝 Output Length: {results['output_length']:,} characters")
        print(f"📁 Check the 'generated_offers' directory for results")
    else:
        print(f"\n❌ Offer generation failed: {results['error']}")

if __name__ == "__main__":
    main()
